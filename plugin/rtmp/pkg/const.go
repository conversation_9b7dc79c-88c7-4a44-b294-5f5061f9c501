package rtmp

import (
	"fmt"
	"time"

	"m7s.live/v5/pkg/util"
)

const (
	PacketTypeSequenceStart byte = iota
	PacketTypeCodedFrames
	PacketTypeSequenceEnd
	PacketTypeCodedFramesX
	PacketTypeMetadata
	PacketTypeMPEG2TSSequenceStart
)

type RTMPData struct {
	Timestamp uint32
	util.RecyclableMemory
}

func (avcc *RTMPData) GetSize() int {
	return avcc.Size
}

func (avcc *RTMPData) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf(`{"Timestamp":%d,"Size":%d,"Data":"%s"}`, avcc.Timestamp, avcc.Size, avcc.String())), nil
}

func (avcc *RTMPData) String() string {
	reader := avcc.NewReader()
	var bytes10 [10]byte
	reader.ReadBytesTo(bytes10[:])
	return fmt.Sprintf("%d % 02X", avcc.Timestamp, bytes10[:])
}

func (avcc *RTMPData) GetTimestamp() time.Duration {
	return time.Duration(avcc.Timestamp) * time.Millisecond
}

func (avcc *RTMPData) WrapAudio() *Audio {
	return &Audio{RTMPData: *avcc}
}

func (avcc *RTMPData) WrapVideo() *Video {
	return &Video{RTMPData: *avcc}
}
